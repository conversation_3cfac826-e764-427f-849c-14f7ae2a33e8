// Test utility for Bokadirekt webhook integration
import { BokadirektWebhookPayload } from './types.ts';

// Sample webhook payloads for testing
export const sampleBookingCreatedPayload: BokadirektWebhookPayload = {
  event_type: 'booking.created',
  event_id: 'evt_test_123456789',
  timestamp: new Date().toISOString(),
  api_version: '2024-01-01',
  data: {
    booking_id: 12345,
    external_booking_id: 'ext_booking_123',
    customer: {
      id: 1001,
      name: '<PERSON>',
      email: 'anna.<PERSON><PERSON><PERSON>@example.com',
      phone: '+46701234567',
      date_of_birth: '1990-05-15',
      gender: 'female',
      address: {
        street: 'Storgatan 123',
        city: 'Stockholm',
        postal_code: '11122',
        country: 'Sweden'
      },
      notes: 'Första besöket',
      created_at: '2024-01-01T10:00:00Z',
      updated_at: '2024-01-01T10:00:00Z'
    },
    service: {
      id: 501,
      name: 'Ansik<PERSON>behandling - Acne',
      description: 'Djuprengörande ansiktsbehandling för acnebenägen hud',
      duration: 60,
      price: 1200,
      category: 'Ansiktsbehandlingar',
      subcategory: 'Acne',
      requires_consultation: true,
      preparation_instructions: 'Undvik makeup 24h före behandling',
      aftercare_instructions: 'Använd mild rengöring i 48h'
    },
    staff: {
      id: 201,
      name: 'Maria Svensson',
      email: '<EMAIL>',
      role: 'Hudterapeut',
      specializations: ['Acne', 'Rosacea', 'Pigmentering']
    },
    clinic: {
      id: 301,
      name: 'AcneSpecialisten Södermalm',
      location: 'Södermalm',
      address: {
        street: 'Götgatan 78',
        city: 'Stockholm',
        postal_code: '11830',
        country: 'Sweden'
      },
      phone: '+46812345678',
      email: '<EMAIL>',
      timezone: 'Europe/Stockholm'
    },
    company: {
      id: 401,
      name: 'AcneSpecialisten',
      organization_number: '556123-4567',
      website: 'https://acnespecialisten.se',
      contact_email: '<EMAIL>',
      contact_phone: '+46812345678'
    },
    booking_time: '2024-07-03T14:00:00Z',
    end_time: '2024-07-03T15:00:00Z',
    status: 'confirmed',
    notes: 'Kund önskar behandling för acne på kinder',
    internal_notes: 'Första besöket - var extra noggrann med konsultation',
    payment_method: 'card',
    total_amount: 1200,
    booking_source: 'online',
    reminder_sent: false,
    confirmation_sent: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    metadata: {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'acne_treatment'
    }
  }
};

export const sampleBookingCancelledPayload: BokadirektWebhookPayload = {
  ...sampleBookingCreatedPayload,
  event_type: 'booking.cancelled',
  event_id: 'evt_test_cancelled_123',
  data: {
    ...sampleBookingCreatedPayload.data,
    status: 'cancelled',
    cancellation_reason: 'Personliga skäl',
    cancellation_time: new Date().toISOString()
  }
};

export const sampleBookingCompletedPayload: BokadirektWebhookPayload = {
  ...sampleBookingCreatedPayload,
  event_type: 'booking.completed',
  event_id: 'evt_test_completed_123',
  data: {
    ...sampleBookingCreatedPayload.data,
    status: 'completed',
    amount_paid: 1200,
    payment_method: 'card'
  }
};

export const sampleBookingNoShowPayload: BokadirektWebhookPayload = {
  ...sampleBookingCreatedPayload,
  event_type: 'booking.no_show',
  event_id: 'evt_test_noshow_123',
  data: {
    ...sampleBookingCreatedPayload.data,
    status: 'no_show',
    notes: 'Kund kom inte till bokad tid'
  }
};

// Test function to send webhook payload
export async function testWebhook(
  webhookUrl: string, 
  payload: BokadirektWebhookPayload, 
  secret?: string
): Promise<Response> {
  const body = JSON.stringify(payload);
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'User-Agent': 'Bokadirekt-Webhook/1.0'
  };

  // Add signature if secret is provided
  if (secret) {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(secret);
    const messageData = encoder.encode(body);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    const signature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    headers['x-bokadirekt-signature'] = `sha256=${signature}`;
  }

  return fetch(webhookUrl, {
    method: 'POST',
    headers,
    body
  });
}

// Test all webhook scenarios
export async function runWebhookTests(webhookUrl: string, secret?: string) {
  console.log('🧪 Running Bokadirekt webhook tests...\n');

  const tests = [
    { name: 'Booking Created', payload: sampleBookingCreatedPayload },
    { name: 'Booking Cancelled', payload: sampleBookingCancelledPayload },
    { name: 'Booking Completed', payload: sampleBookingCompletedPayload },
    { name: 'Booking No Show', payload: sampleBookingNoShowPayload },
  ];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      const response = await testWebhook(webhookUrl, test.payload, secret);
      const result = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${test.name}: SUCCESS`);
        console.log(`   Activity Log ID: ${result.activity_log_id}`);
      } else {
        console.log(`❌ ${test.name}: FAILED`);
        console.log(`   Error: ${result.error}`);
        console.log(`   Details: ${JSON.stringify(result.details || result.message)}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR`);
      console.log(`   ${error.message}`);
    }
    console.log('');
  }

  console.log('🏁 Webhook tests completed!');
}

// Example usage:
// await runWebhookTests('https://your-project.supabase.co/functions/v1/bokadirekt-webhook', 'your-webhook-secret');
