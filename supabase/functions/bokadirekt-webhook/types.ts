// Bokadirekt webhook payload types
export interface BokadirektCustomer {
  id: number;
  name: string;
  email: string;
  phone: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: {
    street: string;
    city: string;
    postal_code: string;
    country: string;
  };
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface BokadirektService {
  id: number;
  name: string;
  description?: string;
  duration: number; // in minutes
  price: number; // in SEK
  category: string;
  subcategory?: string;
  requires_consultation?: boolean;
  preparation_instructions?: string;
  aftercare_instructions?: string;
}

export interface BokadirektStaff {
  id: number;
  name: string;
  email: string;
  role: string;
  specializations?: string[];
  working_hours?: {
    [day: string]: {
      start: string;
      end: string;
    };
  };
}

export interface BokadirektClinic {
  id: number;
  name: string;
  location: string;
  address: {
    street: string;
    city: string;
    postal_code: string;
    country: string;
  };
  phone: string;
  email: string;
  timezone: string;
  working_hours?: {
    [day: string]: {
      start: string;
      end: string;
    };
  };
}

export interface BokadirektCompany {
  id: number;
  name: string;
  organization_number?: string;
  website?: string;
  contact_email: string;
  contact_phone: string;
  billing_address?: {
    street: string;
    city: string;
    postal_code: string;
    country: string;
  };
}

export interface BokadirektBookingData {
  booking_id: number;
  external_booking_id?: string;
  customer: BokadirektCustomer;
  service: BokadirektService;
  staff: BokadirektStaff;
  clinic: BokadirektClinic;
  company: BokadirektCompany;
  booking_time: string; // ISO 8601 datetime
  end_time: string; // ISO 8601 datetime
  status: 'confirmed' | 'cancelled' | 'completed' | 'no_show' | 'rescheduled';
  notes?: string;
  internal_notes?: string;
  cancellation_reason?: string;
  cancellation_time?: string;
  payment_method?: 'cash' | 'card' | 'klarna' | 'swish' | 'invoice';
  amount_paid?: number;
  discount_applied?: number;
  total_amount: number;
  booking_source: 'online' | 'phone' | 'walk_in' | 'admin';
  reminder_sent?: boolean;
  confirmation_sent?: boolean;
  created_at: string;
  updated_at: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface BokadirektWebhookPayload {
  event_type: BokadirektEventType;
  event_id: string;
  timestamp: string; // ISO 8601 datetime
  api_version: string;
  data: BokadirektBookingData;
  previous_data?: Partial<BokadirektBookingData>; // For update events
}

export type BokadirektEventType = 
  | 'booking.created'
  | 'booking.updated'
  | 'booking.cancelled'
  | 'booking.cancelled_by_staff'
  | 'booking.completed'
  | 'booking.no_show'
  | 'booking.rescheduled'
  | 'booking.confirmed'
  | 'booking.reminder_sent'
  | 'payment.received'
  | 'payment.refunded';

// Activity log transformation types
export interface ActivityLogInsert {
  timestamp: string;
  workspace_id: number;
  clinic_id?: number;
  activity_type: string;
  category: string;
  actor_type: 'employee' | 'customer' | 'system';
  actor_id: number;
  actor_name: string;
  target_type?: string;
  target_id?: number;
  target_name?: string;
  details: Record<string, any>;
  metadata_source: 'web_app' | 'mobile_app' | 'api' | 'system';
  metadata_company?: string;
  metadata_clinic?: string;
  metadata_specialist?: string;
  metadata_service?: string;
  metadata_location?: string;
  metadata_ip_address?: string;
  metadata_user_agent?: string;
  is_important: boolean;
}

// Webhook configuration types
export interface WebhookConfig {
  url: string;
  secret: string;
  events: BokadirektEventType[];
  active: boolean;
  retry_policy?: {
    max_retries: number;
    retry_delay: number; // in seconds
    backoff_multiplier: number;
  };
}

// Error types
export interface WebhookError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export interface WebhookResponse {
  success: boolean;
  activity_log_id?: string;
  message: string;
  error?: WebhookError;
}

// Validation schemas
export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

// Event type mappings
export const BOKADIREKT_TO_ACTIVITY_TYPE: Record<BokadirektEventType, string> = {
  'booking.created': 'booking_created',
  'booking.updated': 'booking_created', // Treat updates as creation for now
  'booking.cancelled': 'booking_cancelled_customer',
  'booking.cancelled_by_staff': 'booking_cancelled_specialist',
  'booking.completed': 'booking_completed',
  'booking.no_show': 'booking_no_show',
  'booking.rescheduled': 'booking_rescheduled',
  'booking.confirmed': 'booking_created', // Treat confirmation as creation
  'booking.reminder_sent': 'booking_created', // Log as booking activity
  'payment.received': 'payment_card', // Default to card, can be refined
  'payment.refunded': 'payment_summary',
};

// Company name mappings
export const COMPANY_NAME_MAPPING: Record<string, string> = {
  'AcneSpecialisten': 'AcneSpecialisten',
  'Acne Specialisten': 'AcneSpecialisten',
  'DAHL': 'DAHL',
  'Dahl': 'DAHL',
  'Sveriges Skönhetscenter': 'Sveriges Skönhetscenter',
  'Sveriges Skonhetscenter': 'Sveriges Skönhetscenter',
  'Skonhetscenter': 'Sveriges Skönhetscenter',
};

// Payment method mappings
export const PAYMENT_METHOD_MAPPING: Record<string, string> = {
  'cash': 'payment_cash',
  'card': 'payment_card',
  'klarna': 'payment_klarna',
  'swish': 'payment_swish',
  'invoice': 'payment_summary',
};
