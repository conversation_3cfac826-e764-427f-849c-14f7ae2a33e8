import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import {
  BokadirektWebhookPayload,
  ActivityLogInsert,
  WebhookResponse,
  ValidationResult,
  BOKADIREKT_TO_ACTIVITY_TYPE,
  COMPANY_NAME_MAPPING,
  PAYMENT_METHOD_MAPPING
} from './types.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-bokadirekt-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

// Validation functions
function validateWebhookPayload(payload: any): ValidationResult {
  const errors: string[] = [];

  if (!payload.event_type) errors.push('Missing event_type');
  if (!payload.event_id) errors.push('Missing event_id');
  if (!payload.timestamp) errors.push('Missing timestamp');
  if (!payload.data) errors.push('Missing data object');

  if (payload.data) {
    if (!payload.data.booking_id) errors.push('Missing booking_id');
    if (!payload.data.customer) errors.push('Missing customer data');
    if (!payload.data.service) errors.push('Missing service data');
    if (!payload.data.staff) errors.push('Missing staff data');
    if (!payload.data.clinic) errors.push('Missing clinic data');
    if (!payload.data.company) errors.push('Missing company data');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

async function validateWebhookSignature(payload: string, signature: string, secret: string): Promise<boolean> {
  try {
    if (!signature || !secret) {
      console.warn('Missing signature or secret for webhook validation');
      return false;
    }

    // Remove 'sha256=' prefix if present
    const cleanSignature = signature.replace(/^sha256=/, '');

    const encoder = new TextEncoder();
    const keyData = encoder.encode(secret);
    const messageData = encoder.encode(payload);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    const computedSignature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    return computedSignature === cleanSignature;
  } catch (error) {
    console.error('Signature validation error:', error);
    return false;
  }
}

function transformBokadirektToActivityLog(payload: BokadirektWebhookPayload): ActivityLogInsert {
  const activityType = BOKADIREKT_TO_ACTIVITY_TYPE[payload.event_type];
  if (!activityType) {
    throw new Error(`Unsupported event type: ${payload.event_type}`);
  }

  // Determine actor based on event type
  const isCustomerAction = payload.event_type === 'booking.cancelled';
  const actor = {
    type: isCustomerAction ? 'customer' as const : 'employee' as const,
    id: isCustomerAction ? payload.data.customer.id : payload.data.staff.id,
    name: isCustomerAction ? payload.data.customer.name : payload.data.staff.name,
  };

  // Map company name
  const companyName = COMPANY_NAME_MAPPING[payload.data.company.name] || payload.data.company.name;

  // Determine if this is an important event
  const importantEvents = ['booking.cancelled', 'booking.cancelled_by_staff', 'booking.no_show'];
  const isImportant = importantEvents.includes(payload.event_type);

  return {
    timestamp: new Date(payload.timestamp).toISOString(),
    workspace_id: 1, // Default workspace - adjust as needed
    clinic_id: payload.data.clinic.id,
    activity_type: activityType,
    category: 'bokningar',
    actor_type: actor.type,
    actor_id: actor.id,
    actor_name: actor.name,
    target_type: 'booking',
    target_id: payload.data.booking_id,
    target_name: `${payload.data.service.name} - ${payload.data.customer.name}`,
    details: {
      event_id: payload.event_id,
      booking_id: payload.data.booking_id,
      external_booking_id: payload.data.external_booking_id,
      customer_id: payload.data.customer.id,
      customer_email: payload.data.customer.email,
      customer_phone: payload.data.customer.phone,
      service_id: payload.data.service.id,
      service_name: payload.data.service.name,
      service_duration: payload.data.service.duration,
      amount_cents: payload.data.total_amount * 100, // Convert to cents
      currency: 'SEK',
      booking_time: payload.data.booking_time,
      end_time: payload.data.end_time,
      status: payload.data.status,
      notes: payload.data.notes,
      internal_notes: payload.data.internal_notes,
      cancellation_reason: payload.data.cancellation_reason,
      cancellation_time: payload.data.cancellation_time,
      payment_method: payload.data.payment_method,
      amount_paid_cents: payload.data.amount_paid ? payload.data.amount_paid * 100 : null,
      discount_applied: payload.data.discount_applied,
      booking_source: payload.data.booking_source,
      reminder_sent: payload.data.reminder_sent,
      confirmation_sent: payload.data.confirmation_sent,
    },
    metadata_source: 'api',
    metadata_company: companyName,
    metadata_clinic: payload.data.clinic.name,
    metadata_specialist: payload.data.staff.name,
    metadata_service: payload.data.service.name,
    metadata_location: `${companyName} ${payload.data.clinic.name}`,
    is_important: isImportant,
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get webhook secret from environment
    const webhookSecret = Deno.env.get('BOKADIREKT_WEBHOOK_SECRET')
    if (!webhookSecret) {
      console.error('BOKADIREKT_WEBHOOK_SECRET not configured')
      return new Response(
        JSON.stringify({ error: 'Webhook not properly configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get request body and signature
    const body = await req.text()
    const signature = req.headers.get('x-bokadirekt-signature') || ''

    // Validate webhook signature
    const isValidSignature = await validateWebhookSignature(body, signature, webhookSecret);
    if (!isValidSignature) {
      console.error('Invalid webhook signature')
      return new Response(
        JSON.stringify({ error: 'Invalid signature' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse webhook payload
    let payload: BokadirektWebhookPayload
    try {
      payload = JSON.parse(body)
    } catch (error) {
      console.error('Invalid JSON payload:', error)
      return new Response(
        JSON.stringify({ error: 'Invalid JSON payload' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Validate payload structure
    const validation = validateWebhookPayload(payload);
    if (!validation.valid) {
      console.error('Invalid webhook payload:', validation.errors);
      return new Response(
        JSON.stringify({
          error: 'Invalid payload structure',
          details: validation.errors
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Received Bokadirekt webhook:', {
      event_type: payload.event_type,
      event_id: payload.event_id,
      booking_id: payload.data.booking_id,
      customer: payload.data.customer.name,
    })

    // Transform payload to activity log format
    const activityLog = transformBokadirektToActivityLog(payload)

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Insert activity log
    const { data, error } = await supabase
      .from('activity_logs')
      .insert([activityLog])
      .select()

    if (error) {
      console.error('Error inserting activity log:', error)
      return new Response(
        JSON.stringify({ error: 'Failed to insert activity log', details: error.message }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Successfully inserted activity log:', data[0]?.id)

    return new Response(
      JSON.stringify({ 
        success: true, 
        activity_log_id: data[0]?.id,
        message: 'Webhook processed successfully' 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Webhook processing error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
