
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const ACTIVITY_TYPES = [
  // Bokningar
  'booking_created', 'booking_cancelled_specialist', 'booking_cancelled_customer', 
  'booking_no_show', 'booking_completed', 'booking_rescheduled',
  
  // Beställningar
  'order_created_b2c', 'order_shipped_b2c', 'order_in_transit_b2c', 'order_delivered_b2c',
  'order_created_b2b', 'order_shipped_b2b', 'order_in_transit_b2b', 'order_delivered_b2b',
  
  // Kunder
  'customer_status_active', 'customer_status_inactive', 'customer_status_maintenance',
  'customer_status_under_treatment', 'customer_status_returning',
  
  // Ekonomi
  'payment_cash', 'payment_klarna', 'payment_card', 'payment_swish', 'payment_summary',
  
  // Specialist
  'commission_earned', 'recommendation_given', 'goal_achievement', 'customer_rating_received',
  
  // Behandlingar
  'consultation_completed', 'treatment_completed', 'treatment_course_started',
  'treatment_course_milestone', 'treatment_course_completed', 'treatment_complication',
  
  // Rekommendationer
  'recommendation_given_product', 'recommendation_given_treatment', 'recommendation_purchased',
  'problem_identified', 'problem_solved', 'skin_score_created', 'skin_score_improved',
  
  // Support
  'chat_started', 'email_received', 'call_completed', 'complaint_received', 'complaint_resolved'
] as const

const CATEGORY_MAP = {
  'booking_created': 'bokningar',
  'booking_cancelled_specialist': 'bokningar',
  'booking_cancelled_customer': 'bokningar',
  'booking_no_show': 'bokningar',
  'booking_completed': 'bokningar',
  'booking_rescheduled': 'bokningar',
  'order_created_b2c': 'beställningar',
  'order_shipped_b2c': 'beställningar',
  'order_in_transit_b2c': 'beställningar',
  'order_delivered_b2c': 'beställningar',
  'order_created_b2b': 'beställningar',
  'order_shipped_b2b': 'beställningar',
  'order_in_transit_b2b': 'beställningar',
  'order_delivered_b2b': 'beställningar',
  'customer_status_active': 'kunder',
  'customer_status_inactive': 'kunder',
  'customer_status_maintenance': 'kunder',
  'customer_status_under_treatment': 'kunder',
  'customer_status_returning': 'kunder',
  'payment_cash': 'ekonomi',
  'payment_klarna': 'ekonomi',
  'payment_card': 'ekonomi',
  'payment_swish': 'ekonomi',
  'payment_summary': 'ekonomi',
  'commission_earned': 'specialist',
  'recommendation_given': 'specialist',
  'goal_achievement': 'specialist',
  'customer_rating_received': 'specialist',
  'consultation_completed': 'behandlingar',
  'treatment_completed': 'behandlingar',
  'treatment_course_started': 'behandlingar',
  'treatment_course_milestone': 'behandlingar',
  'treatment_course_completed': 'behandlingar',
  'treatment_complication': 'behandlingar',
  'recommendation_given_product': 'rekommendationer',
  'recommendation_given_treatment': 'rekommendationer',
  'recommendation_purchased': 'rekommendationer',
  'problem_identified': 'rekommendationer',
  'problem_solved': 'rekommendationer',
  'skin_score_created': 'rekommendationer',
  'skin_score_improved': 'rekommendationer',
  'chat_started': 'support',
  'email_received': 'support',
  'call_completed': 'support',
  'complaint_received': 'support',
  'complaint_resolved': 'support'
} as const

const customers = [
  'Anna Andersson', 'Erik Johansson', 'Maria Larsson', 'Johan Petersson', 
  'Lisa Svensson', 'Emma Nilsson', 'Oscar Berg', 'Sofia Lindqvist'
]

const specialists = ['Anna K', 'Lisa M', 'Erik S', 'Maria L', 'Sofia P', 'Jonas E']
const clinics = ['Södermalm', 'Sundbyberg']
const companies = ['AcneSpecialisten', 'DAHL', 'Sveriges Skönhetscenter']
const services = ['Portömning', 'Laser', 'Konsultation', 'HydraFacial', 'Microneedling', 'Chemical Peeling']

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

function generateMockActivity(activityType: typeof ACTIVITY_TYPES[number], index: number) {
  const category = CATEGORY_MAP[activityType]
  const customer = getRandomElement(customers)
  const specialist = getRandomElement(specialists)
  const clinic = getRandomElement(clinics)
  const company = getRandomElement(companies)
  const service = getRandomElement(services)
  
  // Generate timestamp within last 30 days
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000))
  const randomTime = new Date(thirtyDaysAgo.getTime() + Math.random() * (now.getTime() - thirtyDaysAgo.getTime()))

  const baseActivity = {
    timestamp: randomTime.toISOString(),
    workspace_id: 1,
    clinic_id: Math.floor(Math.random() * 10) + 1,
    activity_type: activityType,
    category,
    metadata_source: getRandomElement(['web_app', 'mobile_app', 'api']),
    metadata_company: company,
    metadata_clinic: clinic,
    metadata_specialist: specialist,
    metadata_service: service,
    metadata_location: `${company} ${clinic}`,
    is_important: Math.random() < 0.2
  }

  // Generate specific data based on category
  switch (category) {
    case 'bokningar':
      return {
        ...baseActivity,
        actor_type: activityType.includes('cancelled_customer') ? 'customer' : 'employee',
        actor_id: Math.floor(Math.random() * 1000) + 1,
        actor_name: activityType.includes('cancelled_customer') ? customer : specialist,
        target_type: 'booking',
        target_id: Math.floor(Math.random() * 10000) + 1,
        target_name: `${service} - ${customer}`,
        details: {
          amount_cents: (800 + Math.floor(Math.random() * 2200)) * 100,
          currency: 'SEK',
          treatment_type: service,
          reason: activityType === 'booking_no_show' ? 'Kunde kom inte' : 
                  activityType === 'booking_cancelled_customer' ? 'Personliga skäl' :
                  activityType === 'booking_cancelled_specialist' ? 'Schema-konflikt' : 
                  activityType === 'booking_rescheduled' ? 'Ombokad till nästa vecka' : undefined
        }
      }

    case 'ekonomi':
      return {
        ...baseActivity,
        actor_type: 'customer',
        actor_id: Math.floor(Math.random() * 1000) + 1,
        actor_name: customer,
        target_type: 'payment',
        target_id: Math.floor(Math.random() * 10000) + 1,
        target_name: `Betalning #${Math.floor(Math.random() * 10000) + 1000}`,
        details: {
          amount_cents: (600 + Math.floor(Math.random() * 4400)) * 100,
          currency: 'SEK',
          payment_method: activityType.split('_')[1],
          treatment_type: service
        }
      }

    case 'specialist':
      return {
        ...baseActivity,
        actor_type: 'employee',
        actor_id: Math.floor(Math.random() * 100) + 1,
        actor_name: specialist,
        target_type: activityType === 'customer_rating_received' ? 'customer' : 'recommendation',
        target_id: Math.floor(Math.random() * 1000) + 1,
        target_name: activityType === 'customer_rating_received' ? customer : service,
        details: {
          amount_cents: activityType === 'commission_earned' ? (300 + Math.floor(Math.random() * 1200)) * 100 : undefined,
          currency: activityType === 'commission_earned' ? 'SEK' : undefined,
          rating: activityType === 'customer_rating_received' ? Math.floor(Math.random() * 2) + 4 : undefined,
          commission_rate: activityType === 'commission_earned' ? Math.floor(Math.random() * 20) + 10 : undefined
        }
      }

    default:
      return {
        ...baseActivity,
        actor_type: 'system',
        actor_id: 1,
        actor_name: 'SkinStory System',
        target_type: 'customer',
        target_id: Math.floor(Math.random() * 1000) + 1,
        target_name: customer,
        details: {
          notes: `Generated activity for ${activityType}`
        }
      }
  }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    console.log('Starting to populate activity logs...')
    
    const activities = []
    
    // Generate 5 activities for each type
    for (const activityType of ACTIVITY_TYPES) {
      for (let i = 0; i < 5; i++) {
        activities.push(generateMockActivity(activityType, i))
      }
    }

    console.log(`Generated ${activities.length} activities`)

    // Insert activities in batches of 100
    const batchSize = 100
    let insertedCount = 0
    
    for (let i = 0; i < activities.length; i += batchSize) {
      const batch = activities.slice(i, i + batchSize)
      
      const { data, error } = await supabase
        .from('activity_logs')
        .insert(batch)
      
      if (error) {
        console.error('Error inserting batch:', error)
        throw error
      }
      
      insertedCount += batch.length
      console.log(`Inserted batch: ${insertedCount}/${activities.length}`)
    }

    console.log(`Successfully populated ${insertedCount} activity logs`)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Successfully populated ${insertedCount} activity logs`,
        totalActivities: insertedCount,
        activityTypes: ACTIVITY_TYPES.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error populating activity logs:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
