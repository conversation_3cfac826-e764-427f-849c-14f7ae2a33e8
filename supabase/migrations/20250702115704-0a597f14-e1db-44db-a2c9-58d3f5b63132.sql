-- Create enum types first
CREATE TYPE public.activity_category AS ENUM (
  'bokningar', 'beställningar', 'kunder', 'ekonomi', 'specialist', 'behandlingar', 'rekommendationer', 'support'
);

CREATE TYPE public.activity_type AS ENUM (
  'booking_created', 'booking_cancelled_specialist', 'booking_cancelled_customer', 'booking_no_show', 'booking_completed', 'booking_rescheduled',
  'order_created_b2c', 'order_shipped_b2c', 'order_in_transit_b2c', 'order_delivered_b2c', 'order_created_b2b', 'order_shipped_b2b', 'order_in_transit_b2b', 'order_delivered_b2b',
  'customer_status_active', 'customer_status_inactive', 'customer_status_maintenance', 'customer_status_under_treatment', 'customer_status_returning',
  'payment_cash', 'payment_klarna', 'payment_card', 'payment_swish', 'payment_summary',
  'commission_earned', 'recommendation_given', 'goal_achievement', 'customer_rating_received',
  'consultation_completed', 'treatment_completed', 'treatment_course_started', 'treatment_course_milestone', 'treatment_course_completed', 'treatment_complication',
  'recommendation_given_product', 'recommendation_given_treatment', 'recommendation_purchased', 'problem_identified', 'problem_solved', 'skin_score_created', 'skin_score_improved',
  'chat_started', 'email_received', 'call_completed', 'complaint_received', 'complaint_resolved'
);

CREATE TYPE public.actor_type AS ENUM ('employee', 'customer', 'system');
CREATE TYPE public.target_type AS ENUM ('booking', 'order', 'customer', 'treatment', 'payment', 'employee', 'report', 'consultation', 'recommendation', 'support');
CREATE TYPE public.metadata_source AS ENUM ('web_app', 'mobile_app', 'api', 'system');

-- Create the activity_logs table
CREATE TABLE public.activity_logs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  workspace_id INTEGER NOT NULL,
  clinic_id INTEGER,
  activity_type activity_type NOT NULL,
  category activity_category NOT NULL,
  
  -- Actor information
  actor_type actor_type NOT NULL,
  actor_id INTEGER NOT NULL,
  actor_name TEXT NOT NULL,
  
  -- Target information (optional)
  target_type target_type,
  target_id INTEGER,
  target_name TEXT,
  
  -- Details as JSONB for flexibility
  details JSONB DEFAULT '{}',
  
  -- Metadata
  metadata_source metadata_source NOT NULL DEFAULT 'web_app',
  metadata_ip_address INET,
  metadata_user_agent TEXT,
  metadata_location TEXT,
  metadata_company TEXT,
  metadata_clinic TEXT,
  metadata_specialist TEXT,
  metadata_service TEXT,
  
  is_important BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for activity logs
CREATE POLICY "Users can view activity logs for their workspace" 
ON public.activity_logs 
FOR SELECT 
USING (true); -- For now, allow all authenticated users to view logs

CREATE POLICY "System can insert activity logs" 
ON public.activity_logs 
FOR INSERT 
WITH CHECK (true); -- Allow system to insert logs

-- Create indexes for better performance
CREATE INDEX idx_activity_logs_timestamp ON public.activity_logs(timestamp DESC);
CREATE INDEX idx_activity_logs_workspace ON public.activity_logs(workspace_id);
CREATE INDEX idx_activity_logs_category ON public.activity_logs(category);
CREATE INDEX idx_activity_logs_activity_type ON public.activity_logs(activity_type);
CREATE INDEX idx_activity_logs_important ON public.activity_logs(is_important) WHERE is_important = true;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.created_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;