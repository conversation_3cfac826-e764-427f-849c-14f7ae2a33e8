// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rdvqgqudrezzmqvkeofc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJkdnFncXVkcmV6em1xdmtlb2ZjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0NTcyMjUsImV4cCI6MjA2NzAzMzIyNX0.jN8aFLmzV3UkFJGsTUmqOydBHdn_dF3_rAaaXjUo8uo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});