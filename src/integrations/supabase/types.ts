export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activity_logs: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          actor_id: number
          actor_name: string
          actor_type: Database["public"]["Enums"]["actor_type"]
          category: Database["public"]["Enums"]["activity_category"]
          clinic_id: number | null
          created_at: string
          details: Json | null
          id: string
          is_important: boolean
          metadata_clinic: string | null
          metadata_company: string | null
          metadata_ip_address: unknown | null
          metadata_location: string | null
          metadata_service: string | null
          metadata_source: Database["public"]["Enums"]["metadata_source"]
          metadata_specialist: string | null
          metadata_user_agent: string | null
          target_id: number | null
          target_name: string | null
          target_type: Database["public"]["Enums"]["target_type"] | null
          timestamp: string
          workspace_id: number
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          actor_id: number
          actor_name: string
          actor_type: Database["public"]["Enums"]["actor_type"]
          category: Database["public"]["Enums"]["activity_category"]
          clinic_id?: number | null
          created_at?: string
          details?: Json | null
          id?: string
          is_important?: boolean
          metadata_clinic?: string | null
          metadata_company?: string | null
          metadata_ip_address?: unknown | null
          metadata_location?: string | null
          metadata_service?: string | null
          metadata_source?: Database["public"]["Enums"]["metadata_source"]
          metadata_specialist?: string | null
          metadata_user_agent?: string | null
          target_id?: number | null
          target_name?: string | null
          target_type?: Database["public"]["Enums"]["target_type"] | null
          timestamp?: string
          workspace_id: number
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          actor_id?: number
          actor_name?: string
          actor_type?: Database["public"]["Enums"]["actor_type"]
          category?: Database["public"]["Enums"]["activity_category"]
          clinic_id?: number | null
          created_at?: string
          details?: Json | null
          id?: string
          is_important?: boolean
          metadata_clinic?: string | null
          metadata_company?: string | null
          metadata_ip_address?: unknown | null
          metadata_location?: string | null
          metadata_service?: string | null
          metadata_source?: Database["public"]["Enums"]["metadata_source"]
          metadata_specialist?: string | null
          metadata_user_agent?: string | null
          target_id?: number | null
          target_name?: string | null
          target_type?: Database["public"]["Enums"]["target_type"] | null
          timestamp?: string
          workspace_id?: number
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      activity_category:
        | "bokningar"
        | "beställningar"
        | "kunder"
        | "ekonomi"
        | "specialist"
        | "behandlingar"
        | "rekommendationer"
        | "support"
      activity_type:
        | "booking_created"
        | "booking_cancelled_specialist"
        | "booking_cancelled_customer"
        | "booking_no_show"
        | "booking_completed"
        | "booking_rescheduled"
        | "order_created_b2c"
        | "order_shipped_b2c"
        | "order_in_transit_b2c"
        | "order_delivered_b2c"
        | "order_created_b2b"
        | "order_shipped_b2b"
        | "order_in_transit_b2b"
        | "order_delivered_b2b"
        | "customer_status_active"
        | "customer_status_inactive"
        | "customer_status_maintenance"
        | "customer_status_under_treatment"
        | "customer_status_returning"
        | "payment_cash"
        | "payment_klarna"
        | "payment_card"
        | "payment_swish"
        | "payment_summary"
        | "commission_earned"
        | "recommendation_given"
        | "goal_achievement"
        | "customer_rating_received"
        | "consultation_completed"
        | "treatment_completed"
        | "treatment_course_started"
        | "treatment_course_milestone"
        | "treatment_course_completed"
        | "treatment_complication"
        | "recommendation_given_product"
        | "recommendation_given_treatment"
        | "recommendation_purchased"
        | "problem_identified"
        | "problem_solved"
        | "skin_score_created"
        | "skin_score_improved"
        | "chat_started"
        | "email_received"
        | "call_completed"
        | "complaint_received"
        | "complaint_resolved"
      actor_type: "employee" | "customer" | "system"
      metadata_source: "web_app" | "mobile_app" | "api" | "system"
      target_type:
        | "booking"
        | "order"
        | "customer"
        | "treatment"
        | "payment"
        | "employee"
        | "report"
        | "consultation"
        | "recommendation"
        | "support"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      activity_category: [
        "bokningar",
        "beställningar",
        "kunder",
        "ekonomi",
        "specialist",
        "behandlingar",
        "rekommendationer",
        "support",
      ],
      activity_type: [
        "booking_created",
        "booking_cancelled_specialist",
        "booking_cancelled_customer",
        "booking_no_show",
        "booking_completed",
        "booking_rescheduled",
        "order_created_b2c",
        "order_shipped_b2c",
        "order_in_transit_b2c",
        "order_delivered_b2c",
        "order_created_b2b",
        "order_shipped_b2b",
        "order_in_transit_b2b",
        "order_delivered_b2b",
        "customer_status_active",
        "customer_status_inactive",
        "customer_status_maintenance",
        "customer_status_under_treatment",
        "customer_status_returning",
        "payment_cash",
        "payment_klarna",
        "payment_card",
        "payment_swish",
        "payment_summary",
        "commission_earned",
        "recommendation_given",
        "goal_achievement",
        "customer_rating_received",
        "consultation_completed",
        "treatment_completed",
        "treatment_course_started",
        "treatment_course_milestone",
        "treatment_course_completed",
        "treatment_complication",
        "recommendation_given_product",
        "recommendation_given_treatment",
        "recommendation_purchased",
        "problem_identified",
        "problem_solved",
        "skin_score_created",
        "skin_score_improved",
        "chat_started",
        "email_received",
        "call_completed",
        "complaint_received",
        "complaint_resolved",
      ],
      actor_type: ["employee", "customer", "system"],
      metadata_source: ["web_app", "mobile_app", "api", "system"],
      target_type: [
        "booking",
        "order",
        "customer",
        "treatment",
        "payment",
        "employee",
        "report",
        "consultation",
        "recommendation",
        "support",
      ],
    },
  },
} as const
