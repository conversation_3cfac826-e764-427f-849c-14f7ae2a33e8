import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw, 
  AlertTriangle,
  Webhook,
  Database,
  TrendingUp
} from "lucide-react";
import { useActivityLogs } from '@/hooks/useActivityLogs';
import { format } from 'date-fns';
import { sv } from 'date-fns/locale';

interface WebhookStats {
  totalWebhooks: number;
  successfulWebhooks: number;
  failedWebhooks: number;
  lastWebhookTime?: Date;
  webhookTypes: Record<string, number>;
}

interface WebhookEvent {
  id: string;
  timestamp: Date;
  event_type: string;
  status: 'success' | 'failed';
  booking_id?: number;
  customer_name?: string;
  error_message?: string;
}

const WebhookMonitor: React.FC = () => {
  const [stats, setStats] = useState<WebhookStats>({
    totalWebhooks: 0,
    successfulWebhooks: 0,
    failedWebhooks: 0,
    webhookTypes: {}
  });
  
  const [recentEvents, setRecentEvents] = useState<WebhookEvent[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch recent webhook-generated activities
  const { 
    data: activities = [], 
    isLoading, 
    refetch 
  } = useActivityLogs({
    limit: 50,
    dateFrom: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
  });

  // Process activities to extract webhook statistics
  useEffect(() => {
    if (activities.length === 0) return;

    // Filter activities that came from webhooks (metadata_source = 'api')
    const webhookActivities = activities.filter(
      activity => activity.metadata.source === 'api' && 
                 activity.category === 'bokningar'
    );

    // Calculate statistics
    const webhookTypes: Record<string, number> = {};
    webhookActivities.forEach(activity => {
      const eventType = getEventTypeFromActivity(activity.activity_type);
      webhookTypes[eventType] = (webhookTypes[eventType] || 0) + 1;
    });

    const newStats: WebhookStats = {
      totalWebhooks: webhookActivities.length,
      successfulWebhooks: webhookActivities.length, // All stored activities are successful
      failedWebhooks: 0, // We don't store failed webhooks in activity logs
      lastWebhookTime: webhookActivities.length > 0 ? webhookActivities[0].timestamp : undefined,
      webhookTypes
    };

    setStats(newStats);

    // Convert activities to webhook events
    const events: WebhookEvent[] = webhookActivities.slice(0, 10).map(activity => ({
      id: activity.id,
      timestamp: activity.timestamp,
      event_type: getEventTypeFromActivity(activity.activity_type),
      status: 'success',
      booking_id: activity.details.booking_id,
      customer_name: activity.target?.name?.split(' - ')[1] || 'Unknown'
    }));

    setRecentEvents(events);
  }, [activities]);

  const getEventTypeFromActivity = (activityType: string): string => {
    const mapping: Record<string, string> = {
      'booking_created': 'booking.created',
      'booking_cancelled_customer': 'booking.cancelled',
      'booking_cancelled_specialist': 'booking.cancelled_by_staff',
      'booking_completed': 'booking.completed',
      'booking_no_show': 'booking.no_show',
      'booking_rescheduled': 'booking.rescheduled'
    };
    return mapping[activityType] || activityType;
  };

  const getEventTypeColor = (eventType: string): string => {
    switch (eventType) {
      case 'booking.created':
        return 'bg-green-100 text-green-800';
      case 'booking.cancelled':
      case 'booking.cancelled_by_staff':
        return 'bg-red-100 text-red-800';
      case 'booking.completed':
        return 'bg-blue-100 text-blue-800';
      case 'booking.no_show':
        return 'bg-orange-100 text-orange-800';
      case 'booking.rescheduled':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refetch();
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const successRate = stats.totalWebhooks > 0 
    ? (stats.successfulWebhooks / stats.totalWebhooks) * 100 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Webhook Monitor</h2>
          <p className="text-gray-600">Bokadirekt webhook integration status</p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Uppdatera
        </Button>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Totalt</p>
                <p className="text-2xl font-bold">{stats.totalWebhooks}</p>
              </div>
              <Webhook className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Lyckade</p>
                <p className="text-2xl font-bold text-green-600">{stats.successfulWebhooks}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Misslyckade</p>
                <p className="text-2xl font-bold text-red-600">{stats.failedWebhooks}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Framgång</p>
                <p className="text-2xl font-bold">{successRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Last Webhook Status */}
      {stats.lastWebhookTime && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            Senaste webhook mottagen: {format(stats.lastWebhookTime, 'PPpp', { locale: sv })}
          </AlertDescription>
        </Alert>
      )}

      {/* Webhook Types Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Webhook-typer (senaste 24h)
          </CardTitle>
          <CardDescription>
            Fördelning av olika webhook-händelser
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(stats.webhookTypes).map(([eventType, count]) => (
              <div key={eventType} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <Badge className={getEventTypeColor(eventType)}>
                    {eventType}
                  </Badge>
                </div>
                <span className="font-semibold">{count}</span>
              </div>
            ))}
            {Object.keys(stats.webhookTypes).length === 0 && (
              <div className="col-span-full text-center text-gray-500 py-4">
                Inga webhook-händelser senaste 24h
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Senaste händelser
          </CardTitle>
          <CardDescription>
            De 10 senaste webhook-händelserna
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-4">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p className="text-gray-500">Laddar händelser...</p>
            </div>
          ) : recentEvents.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-gray-500">Inga webhook-händelser hittades</p>
              <p className="text-sm text-gray-400 mt-1">
                Kontrollera att webhook är konfigurerad i Bokadirekt
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {event.status === 'success' ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge className={getEventTypeColor(event.event_type)}>
                          {event.event_type}
                        </Badge>
                        {event.booking_id && (
                          <span className="text-sm text-gray-500">
                            Bokning #{event.booking_id}
                          </span>
                        )}
                      </div>
                      {event.customer_name && (
                        <p className="text-sm text-gray-600 mt-1">
                          {event.customer_name}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {format(event.timestamp, 'HH:mm', { locale: sv })}
                    </p>
                    <p className="text-xs text-gray-400">
                      {format(event.timestamp, 'dd MMM', { locale: sv })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WebhookMonitor;
