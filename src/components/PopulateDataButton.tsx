
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

const PopulateDataButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handlePopulateData = async () => {
    setIsLoading(true);
    
    try {
      const { data, error } = await supabase.functions.invoke('populate-activity-logs');
      
      if (error) {
        throw error;
      }

      toast({
        title: "Success!",
        description: `Successfully populated ${data.totalActivities} activity logs`,
      });
      
    } catch (error) {
      console.error('Error populating data:', error);
      toast({
        title: "Error",
        description: "Failed to populate activity logs. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      onClick={handlePopulateData} 
      disabled={isLoading}
      className="flex items-center gap-2"
    >
      {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
      {isLoading ? 'Populating...' : 'Populate Activity Logs'}
    </Button>
  );
};

export default PopulateDataButton;
