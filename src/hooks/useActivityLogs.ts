
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ExtendedActivityLog, ActivityCategory, ActivityType, ActivityDetails } from '@/types/activity';

interface ActivityLogFilters {
  category?: ActivityCategory;
  activityType?: ActivityType;
  isImportant?: boolean;
  limit?: number;
  offset?: number;
  dateFrom?: Date;
  dateTo?: Date;
}

export const useActivityLogs = (filters: ActivityLogFilters = {}) => {
  return useQuery({
    queryKey: ['activity-logs', filters],
    queryFn: async () => {
      let query = supabase
        .from('activity_logs')
        .select('*')
        .order('timestamp', { ascending: false });

      // Apply filters
      if (filters.category) {
        query = query.eq('category', filters.category);
      }

      if (filters.activityType) {
        query = query.eq('activity_type', filters.activityType);
      }

      if (filters.isImportant !== undefined) {
        query = query.eq('is_important', filters.isImportant);
      }

      if (filters.dateFrom) {
        query = query.gte('timestamp', filters.dateFrom.toISOString());
      }

      if (filters.dateTo) {
        query = query.lte('timestamp', filters.dateTo.toISOString());
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // Transform database records to ExtendedActivityLog format
      return data.map((record): ExtendedActivityLog => ({
        id: record.id,
        timestamp: new Date(record.timestamp),
        workspace_id: record.workspace_id,
        clinic_id: record.clinic_id,
        activity_type: record.activity_type,
        category: record.category,
        actor: {
          type: record.actor_type,
          id: record.actor_id,
          name: record.actor_name
        },
        target: record.target_type ? {
          type: record.target_type,
          id: record.target_id!,
          name: record.target_name!
        } : undefined,
        details: (record.details as ActivityDetails) || {},
        metadata: {
          source: record.metadata_source,
          company: record.metadata_company as 'AcneSpecialisten' | 'DAHL' | 'Sveriges Skönhetscenter' | undefined,
          clinic: record.metadata_clinic || undefined,
          specialist: record.metadata_specialist || undefined,
          service: record.metadata_service || undefined,
          location: record.metadata_location || undefined,
          ip_address: record.metadata_ip_address as string || undefined,
          user_agent: record.metadata_user_agent || undefined
        },
        is_important: record.is_important,
        created_at: new Date(record.created_at)
      }));
    },
  });
};

export const useRecentActivityLogs = (limit: number = 10) => {
  return useActivityLogs({ limit });
};
