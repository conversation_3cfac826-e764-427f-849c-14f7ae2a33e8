
import { BodyRegion } from './service-interfaces';

export const BODY_REGIONS: BodyRegion[] = [
  {
    id: 'head',
    name: '<PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> och hals',
    icon: '👤'
  },
  {
    id: 'upper-body',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON><PERSON>, armar och axlar',
    icon: '🫸'
  },
  {
    id: 'lower-body',
    name: '<PERSON>krop<PERSON>',
    description: '<PERSON>, höfter och fötter',
    icon: '🦵'
  },
  {
    id: 'intim',
    name: 'Intim',
    description: 'Intimområden',
    icon: '🔒'
  }
];
