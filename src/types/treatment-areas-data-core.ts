
import { TreatmentArea } from './service-interfaces';

export const TREATMENT_AREAS: TreatmentArea[] = [
  // Head region
  {
    id: 'face',
    name: '<PERSON><PERSON><PERSON>',
    regionId: 'head',
    description: '<PERSON><PERSON> an<PERSON>'
  },
  {
    id: 'neck',
    name: '<PERSON><PERSON>',
    regionId: 'head',
    description: 'Halsområ<PERSON>'
  },
  
  // Upper body region
  {
    id: 'chest',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    regionId: 'upper-body',
    description: 'Bröstkorg och dekolletage'
  },
  {
    id: 'arms',
    name: '<PERSON><PERSON>',
    regionId: 'upper-body',
    description: 'Över- och underarmar'
  },
  {
    id: 'shoulders',
    name: '<PERSON><PERSON><PERSON>',
    regionId: 'upper-body',
    description: 'Axelområ<PERSON>'
  },
  {
    id: 'hands',
    name: '<PERSON><PERSON><PERSON>',
    regionId: 'upper-body',
    description: '<PERSON><PERSON><PERSON> och fingrar'
  },
  {
    id: 'back',
    name: '<PERSON>y<PERSON>',
    regionId: 'upper-body',
    description: 'R<PERSON>ggens olika områden'
  },
  
  // Lower body region
  {
    id: 'legs',
    name: '<PERSON>',
    regionId: 'lower-body',
    description: '<PERSON><PERSON><PERSON> och vader'
  },
  {
    id: 'feet',
    name: '<PERSON><PERSON><PERSON>',
    regionId: 'lower-body',
    description: 'Fötter och tår'
  },
  
  // Intim region
  {
    id: 'bikini',
    name: 'Bikiniområde',
    regionId: 'intim',
    description: 'Intimområdet'
  },
  {
    id: 'brazilian',
    name: 'Brazilian',
    regionId: 'intim',
    description: 'Fullständig intimbehandling'
  }
];
