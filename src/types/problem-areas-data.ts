
import { ProblemArea } from './service-interfaces';

export const PROBLEM_AREAS: ProblemArea[] = [
  {
    id: 'acne',
    name: '<PERSON>k<PERSON>',
    description: 'Inflammerad och icke-inflammerad akne'
  },
  {
    id: 'scars',
    name: '<PERSON>rr',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON> och andra hud<PERSON>rr'
  },
  {
    id: 'wrinkles',
    name: '<PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON> linjer och djupare rynkor'
  },
  {
    id: 'pigmentation',
    name: 'Pigmentering',
    description: 'Ojämn pigmentering och fläckar'
  },
  {
    id: 'dry-skin',
    name: '<PERSON><PERSON> hud',
    description: 'Utto<PERSON>d och irriterad hud'
  },
  {
    id: 'large-pores',
    name: '<PERSON><PERSON> porer',
    description: 'Förstorade och synliga porer'
  },
  {
    id: 'dullness',
    name: '<PERSON> hud',
    description: '<PERSON><PERSON><PERSON><PERSON> och glansös hud'
  },
  {
    id: 'rosacea',
    name: '<PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> och känslig hud'
  },
  {
    id: 'hair-removal',
    name: '<PERSON><PERSON><PERSON><PERSON>tag<PERSON>',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON> hårväxt'
  },
  {
    id: 'vascular-lesions',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ringar',
    description: 'Synliga blodkärl och rosacearodnad'
  }
];
