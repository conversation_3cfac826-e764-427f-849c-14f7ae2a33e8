
export interface BaseService {
  id: string;
  name: string;
  description: string;
  category: string;
}

export interface ProblemArea {
  id: string;
  name: string;
  description: string;
}

export interface BodyRegion {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface TreatmentArea {
  id: string;
  name: string;
  regionId: string;
  description?: string;
}

export interface TreatmentZone {
  id: string;
  name: string;
  areaId: string;
  description?: string;
  isPopular?: boolean;
}

export interface CompositeService {
  id?: string;
  baseServiceId: string;
  selectedEquipment: string[];
  selectedProblems: string[];
  selectedAreas: string[];
  selectedZones?: string[];
  customName: string;
  description: string;
  autoGeneratedKeywords: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
