# Bokadirekt Webhook Integration

This document describes the webhook integration between Bokadirekt (booking system) and SkinStory's activity log system.

## Overview

The integration automatically captures booking events from Bokadirekt and transforms them into activity records in our Supabase `activity_logs` table. This provides real-time visibility into booking activities across all SkinStory brands.

## Architecture

```
Bokadirekt → Webhook → Supabase Edge Function → Activity Logs Table → SkinStory Dashboard
```

## Supported Events

The webhook handles the following Bokadirekt events:

| Bokadirekt Event | SkinStory Activity Type | Category | Important |
|------------------|------------------------|----------|-----------|
| `booking.created` | `booking_created` | `bokningar` | No |
| `booking.cancelled` | `booking_cancelled_customer` | `bokningar` | Yes |
| `booking.cancelled_by_staff` | `booking_cancelled_specialist` | `bokningar` | Yes |
| `booking.completed` | `booking_completed` | `bokningar` | No |
| `booking.no_show` | `booking_no_show` | `bokningar` | Yes |
| `booking.rescheduled` | `booking_rescheduled` | `bokningar` | No |
| `payment.received` | `payment_card` | `ekonomi` | No |
| `payment.refunded` | `payment_summary` | `ekonomi` | Yes |

## Setup Instructions

### 1. Deploy the Supabase Edge Function

```bash
# Deploy the webhook function
supabase functions deploy bokadirekt-webhook

# Set environment variables
supabase secrets set BOKADIREKT_WEBHOOK_SECRET=your_webhook_secret_here
```

### 2. Configure Bokadirekt Webhook

In your Bokadirekt admin panel:

1. Navigate to **Settings** → **Webhooks**
2. Click **Add New Webhook**
3. Configure the webhook:
   - **URL**: `https://your-project.supabase.co/functions/v1/bokadirekt-webhook`
   - **Secret**: Use the same secret you set in Supabase
   - **Events**: Select the events you want to track
   - **Active**: Enable the webhook

### 3. Test the Integration

Use the provided test utility:

```typescript
import { runWebhookTests } from './supabase/functions/bokadirekt-webhook/test-webhook.ts';

await runWebhookTests(
  'https://your-project.supabase.co/functions/v1/bokadirekt-webhook',
  'your-webhook-secret'
);
```

## Webhook Payload Structure

### Example Booking Created Event

```json
{
  "event_type": "booking.created",
  "event_id": "evt_123456789",
  "timestamp": "2024-07-03T12:00:00Z",
  "api_version": "2024-01-01",
  "data": {
    "booking_id": 12345,
    "customer": {
      "id": 1001,
      "name": "Anna Andersson",
      "email": "<EMAIL>",
      "phone": "+46701234567"
    },
    "service": {
      "id": 501,
      "name": "Ansiktsbehandling - Acne",
      "duration": 60,
      "price": 1200
    },
    "staff": {
      "id": 201,
      "name": "Maria Svensson"
    },
    "clinic": {
      "id": 301,
      "name": "AcneSpecialisten Södermalm",
      "location": "Södermalm"
    },
    "company": {
      "id": 401,
      "name": "AcneSpecialisten"
    },
    "booking_time": "2024-07-03T14:00:00Z",
    "end_time": "2024-07-03T15:00:00Z",
    "status": "confirmed",
    "payment_method": "card",
    "total_amount": 1200,
    "booking_source": "online"
  }
}
```

## Data Transformation

The webhook transforms Bokadirekt data into our activity log format:

### Mapping Rules

- **Actor**: Determined by event type (customer for cancellations, employee for others)
- **Target**: Always set to 'booking' with booking ID
- **Company**: Mapped to standard SkinStory brand names
- **Amount**: Converted from SEK to cents (multiply by 100)
- **Importance**: Set to true for cancellations and no-shows

### Activity Log Record Example

```json
{
  "timestamp": "2024-07-03T12:00:00Z",
  "workspace_id": 1,
  "clinic_id": 301,
  "activity_type": "booking_created",
  "category": "bokningar",
  "actor_type": "employee",
  "actor_id": 201,
  "actor_name": "Maria Svensson",
  "target_type": "booking",
  "target_id": 12345,
  "target_name": "Ansiktsbehandling - Acne - Anna Andersson",
  "details": {
    "event_id": "evt_123456789",
    "booking_id": 12345,
    "customer_id": 1001,
    "customer_email": "<EMAIL>",
    "customer_phone": "+46701234567",
    "service_id": 501,
    "service_name": "Ansiktsbehandling - Acne",
    "amount_cents": 120000,
    "currency": "SEK",
    "booking_time": "2024-07-03T14:00:00Z",
    "status": "confirmed",
    "payment_method": "card",
    "booking_source": "online"
  },
  "metadata_source": "api",
  "metadata_company": "AcneSpecialisten",
  "metadata_clinic": "AcneSpecialisten Södermalm",
  "metadata_specialist": "Maria Svensson",
  "metadata_service": "Ansiktsbehandling - Acne",
  "metadata_location": "AcneSpecialisten AcneSpecialisten Södermalm",
  "is_important": false
}
```

## Security

### Webhook Signature Validation

The webhook validates incoming requests using HMAC SHA-256 signatures:

1. Bokadirekt signs the payload with your webhook secret
2. The signature is sent in the `x-bokadirekt-signature` header
3. Our function validates the signature before processing

### Environment Variables

Required environment variables:

- `BOKADIREKT_WEBHOOK_SECRET`: Secret key for webhook signature validation
- `SUPABASE_URL`: Your Supabase project URL (automatically set)
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key (automatically set)

## Error Handling

The webhook handles various error scenarios:

- **Invalid signature**: Returns 401 Unauthorized
- **Malformed JSON**: Returns 400 Bad Request
- **Missing required fields**: Returns 400 Bad Request with validation errors
- **Unsupported event type**: Returns 400 Bad Request
- **Database errors**: Returns 500 Internal Server Error

## Monitoring

### Logs

Check webhook logs in Supabase Dashboard:
1. Go to **Edge Functions** → **bokadirekt-webhook**
2. View **Logs** tab for real-time monitoring

### Activity Log Verification

Verify webhook data in the SkinStory dashboard:
1. Navigate to **Aktivitetslogg**
2. Filter by category "Bokningar"
3. Look for recent booking activities

## Troubleshooting

### Common Issues

1. **No activities appearing**
   - Check webhook URL is correct
   - Verify webhook secret matches
   - Check Supabase function logs

2. **Signature validation failing**
   - Ensure webhook secret is set correctly
   - Check Bokadirekt webhook configuration

3. **Data transformation errors**
   - Verify Bokadirekt payload structure
   - Check for missing required fields

### Testing

Use the test utility to verify the integration:

```bash
# Test with sample payloads
deno run --allow-net supabase/functions/bokadirekt-webhook/test-webhook.ts
```

## Support

For issues with the webhook integration:

1. Check Supabase function logs
2. Verify Bokadirekt webhook configuration
3. Test with sample payloads
4. Contact development team with error details
