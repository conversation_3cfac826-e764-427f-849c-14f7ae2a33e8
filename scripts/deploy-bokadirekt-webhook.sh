#!/bin/bash

# Bokadirekt Webhook Integration Deployment Script
# This script deploys the webhook function and sets up the necessary configuration

set -e

echo "🚀 Deploying Bokadirekt Webhook Integration..."
echo ""

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're logged in to Supabase
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please login first:"
    echo "   supabase login"
    exit 1
fi

# Get current project
PROJECT_REF=$(supabase status | grep "Project URL" | awk '{print $3}' | sed 's/.*\/\/\([^.]*\).*/\1/')
if [ -z "$PROJECT_REF" ]; then
    echo "❌ No Supabase project found. Please link your project first:"
    echo "   supabase link --project-ref your-project-ref"
    exit 1
fi

echo "📋 Project Reference: $PROJECT_REF"
echo ""

# Prompt for webhook secret
echo "🔐 Setting up webhook security..."
read -p "Enter your Bokadirekt webhook secret (or press Enter to generate one): " WEBHOOK_SECRET

if [ -z "$WEBHOOK_SECRET" ]; then
    # Generate a random secret
    WEBHOOK_SECRET=$(openssl rand -hex 32)
    echo "✅ Generated webhook secret: $WEBHOOK_SECRET"
    echo "⚠️  Save this secret - you'll need it for Bokadirekt configuration!"
else
    echo "✅ Using provided webhook secret"
fi

echo ""

# Set the webhook secret
echo "🔧 Setting webhook secret in Supabase..."
supabase secrets set BOKADIREKT_WEBHOOK_SECRET="$WEBHOOK_SECRET"

if [ $? -eq 0 ]; then
    echo "✅ Webhook secret set successfully"
else
    echo "❌ Failed to set webhook secret"
    exit 1
fi

echo ""

# Deploy the Edge Function
echo "📦 Deploying webhook Edge Function..."
supabase functions deploy bokadirekt-webhook

if [ $? -eq 0 ]; then
    echo "✅ Webhook function deployed successfully"
else
    echo "❌ Failed to deploy webhook function"
    exit 1
fi

echo ""

# Get the webhook URL
WEBHOOK_URL="https://$PROJECT_REF.supabase.co/functions/v1/bokadirekt-webhook"

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Configuration Summary:"
echo "   Webhook URL: $WEBHOOK_URL"
echo "   Webhook Secret: $WEBHOOK_SECRET"
echo ""
echo "📝 Next Steps:"
echo ""
echo "1. Configure Bokadirekt Webhook:"
echo "   - Login to your Bokadirekt admin panel"
echo "   - Navigate to Settings → Webhooks"
echo "   - Add new webhook with URL: $WEBHOOK_URL"
echo "   - Set secret to: $WEBHOOK_SECRET"
echo "   - Select events: booking.created, booking.cancelled, booking.completed, etc."
echo ""
echo "2. Test the integration:"
echo "   - Use the test utility in supabase/functions/bokadirekt-webhook/test-webhook.ts"
echo "   - Or create a test booking in Bokadirekt"
echo "   - Check the SkinStory Aktivitetslogg page for new activities"
echo ""
echo "3. Monitor the webhook:"
echo "   - Check Supabase Dashboard → Edge Functions → bokadirekt-webhook → Logs"
echo "   - Monitor activity logs in SkinStory dashboard"
echo ""

# Offer to run tests
echo "🧪 Would you like to run webhook tests now? (y/n)"
read -p "> " RUN_TESTS

if [ "$RUN_TESTS" = "y" ] || [ "$RUN_TESTS" = "Y" ]; then
    echo ""
    echo "🧪 Running webhook tests..."
    
    # Create a simple test script
    cat > /tmp/test-webhook.js << EOF
import { runWebhookTests } from './supabase/functions/bokadirekt-webhook/test-webhook.ts';

await runWebhookTests('$WEBHOOK_URL', '$WEBHOOK_SECRET');
EOF

    # Run the test (requires Deno)
    if command -v deno &> /dev/null; then
        deno run --allow-net /tmp/test-webhook.js
        rm /tmp/test-webhook.js
    else
        echo "⚠️  Deno not found. To run tests manually:"
        echo "   1. Install Deno: https://deno.land/manual/getting_started/installation"
        echo "   2. Run: deno run --allow-net supabase/functions/bokadirekt-webhook/test-webhook.ts"
    fi
fi

echo ""
echo "✨ Bokadirekt webhook integration is ready!"
echo ""
echo "📚 For more information, see: docs/bokadirekt-webhook-integration.md"
